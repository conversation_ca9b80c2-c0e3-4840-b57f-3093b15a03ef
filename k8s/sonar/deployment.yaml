apiVersion: apps/v1
kind: Deployment
metadata:
  name: sonarqube
  namespace: sonarqube
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sonarqube
  template:
    metadata:
      labels:
        app: sonarqube
    spec:
      securityContext:
        fsGroup: 1000
      initContainers:
      - name: init-sysctl
        image: busybox:1.35
        imagePullPolicy: IfNotPresent
        securityContext:
          privileged: true
        command: ["sh", "-c", "sysctl -w vm.max_map_count=524288 && sysctl -w fs.file-max=131072"]
      containers:
      - name: sonarqube
        image: sonarqube:latest
        ports:
        - containerPort: 9000
          name: http
        env:
        - name: SONAR_JDBC_USERNAME
          valueFrom:
            secretKeyRef:
              name: sonarqube-db-secret
              key: username
        - name: SONAR_JDBC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: sonarqube-db-secret
              key: password
        envFrom:
        - configMapRef:
            name: sonarqube-config
        volumeMounts:
        - name: sonarqube-data
          mountPath: /opt/sonarqube/data
        - name: sonarqube-logs
          mountPath: /opt/sonarqube/logs
        - name: sonarqube-extensions
          mountPath: /opt/sonarqube/extensions
        livenessProbe:
          httpGet:
            path: /api/system/status
            port: 9000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 6
        readinessProbe:
          httpGet:
            path: /api/system/status
            port: 9000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 6
      volumes:
      - name: sonarqube-data
        persistentVolumeClaim:
          claimName: sonarqube-data
      - name: sonarqube-logs
        persistentVolumeClaim:
          claimName: sonarqube-logs
      - name: sonarqube-extensions
        persistentVolumeClaim:
          claimName: sonarqube-extensions
