apiVersion: apps/v1
kind: Deployment
metadata:
  name: servico-preco
  namespace: preco
spec:
  replicas: 1
  selector:
    matchLabels:
      app: servico-preco
  template:
    metadata:
      labels:
        app: servico-preco
    spec:
      # serviceAccountName: preco-db-init
      # initContainers:
      #   - name: db-init
      #     image: bitnami/postgresql:latest
      #     command:
      #       - /bin/bash
      #       - -c
      #       - /scripts/db-init.sh
      #     env:
      #       - name: TZ
      #         value: "America/Sao_Paulo"
      #     volumeMounts:
      #       - name: db-scripts
      #         mountPath: /scripts
      containers:
        - name: servico-preco
          image: gru.ocir.io/grbvypj1mx3p/postoaki-servico-preco:**************
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          env:
            - name: TZ
              value: "America/Sao_Paulo"
            - name: JAVA_OPTS
              valueFrom:
                configMapKeyRef:
                  name: servico-preco-java-opts
                  key: JAVA_OPTS
          resources:
            limits:
              memory: "4Gi"
              cpu: "2"
            requests:
              memory: "512Mi"
              cpu: "256m"
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 10
          lifecycle:
            postStart:
              exec:
                command:
                - /bin/sh
                - -c
                - |
                  # Aguardar a aplicação iniciar completamente
                  sleep 60
                  # Executar a sincronização
                  curl --location 'https://api.postoaki.com/api/v1/suporte/sync'
      volumes:
        - name: db-scripts
          configMap:
            name: preco-db-scripts
            defaultMode: 0755
      imagePullSecrets:
        - name: docker-registry
