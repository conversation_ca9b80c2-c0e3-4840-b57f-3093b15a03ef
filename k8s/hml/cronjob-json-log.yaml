apiVersion: v1
kind: ConfigMap
metadata:
  name: send-req-logs-by-api-script
  namespace: hml
data:
  send-req-logs-by-api.sh: |
    #!/bin/sh

    TIMESTAMP=$(date +%F_%T | tr ':' '-')

    NAMESPACE="hml"
    LABEL="app=api"

    echo "Create directory to store json files..."
    DIR_JSON=/tmp/json-k8s-homologacao-$TIMESTAMP
    mkdir $DIR_JSON
    ARQUIVO_JSON_ZIP=$DIR_JSON.zip

    echo "Getting pods with label $LABEL in namespace $NAMESPACE..."
    PODS=$(kubectl get pods -n $NAMESPACE -l $LABEL -o jsonpath='{.items[*].metadata.name}')

    for POD in $PODS; do
      echo "Processing pod $POD..."

      ARQUIVO_JSON_REQUEST=json-homologacao-request-$POD-$TIMESTAMP.json
      ARQUIVO_JSON_RESPONSE=json-homologacao-response-$POD-$TIMESTAMP.json

      echo "Copying json request from pod $POD..."
      kubectl cp $NAMESPACE/$POD:/usr/local/tomcat/json-request.log $DIR_JSON/$ARQUIVO_JSON_REQUEST

      echo "Copying json response from pod $POD..."
      kubectl cp $NAMESPACE/$POD:/usr/local/tomcat/json-response.log $DIR_JSON/$ARQUIVO_JSON_RESPONSE

      echo "Removing json from pod $POD..."
      kubectl exec -n $NAMESPACE $POD -- rm /usr/local/tomcat/json-request.log
      kubectl exec -n $NAMESPACE $POD -- rm /usr/local/tomcat/json-response.log
    done

    echo "Compressing json request and response $ARQUIVO_JSON_ZIP..."
    zip -r $ARQUIVO_JSON_ZIP $DIR_JSON

    echo "Requesting drive to sync file..."
    BASE_URL="http://owncloud.owncloud.svc.cluster.local:8080/remote.php/dav/files"
    USERNAME="postoaki"
    PASSWORD="WODOP-GYUZM-MZBCB-AFIYP"
    LOCAL_FILE="$ARQUIVO_JSON_ZIP"
    DESTINATION="/json-homologacao/$(basename $ARQUIVO_JSON_ZIP)"

    # Fazer o upload do arquivo
    curl -u "$USERNAME:$PASSWORD" \
        -T "$LOCAL_FILE" \
        "$BASE_URL/$USERNAME$DESTINATION"

    # Verificar o status da requisição
    if [ $? -eq 0 ]; then
        echo "Arquivo enviado com sucesso para $DESTINATION!"
    else
        echo "Erro ao enviar o arquivo."
    fi


    echo "Removing json and zip..."
    rm -rf $DIR_JSON
    rm -rf $ARQUIVO_JSON_ZIP
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: send-log-cronjob
  namespace: hml
spec:
  schedule: "0,5,10,15,20,25,30,35,40,45,50,55 * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          nodeSelector:
            name: node-pool-hml
          containers:
            - name: send-log
              image: gru.ocir.io/grbvypj1mx3p/postoaki-kubectl:1.0.2
              imagePullPolicy: Always
              command: ["/bin/sh", "-c", "/scripts/send-req-logs-by-api.sh"]
              volumeMounts:
                - name: script-volume
                  mountPath: /scripts
          restartPolicy: Never
          volumes:
            - name: script-volume
              configMap:
                name: send-req-logs-by-api-script
                defaultMode: 0775
          serviceAccountName: copy-log-sa
          imagePullSecrets:
            - name: docker-registry
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: copy-log-sa
  namespace: hml
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: hml
  name: pod-reader
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/exec", "pods/log"]
    verbs: ["get", "list", "create", "delete", "exec"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: read-pods
  namespace: hml
subjects:
  - kind: ServiceAccount
    name: copy-log-sa
    namespace: hml
roleRef:
  kind: Role
  name: pod-reader
  apiGroup: rbac.authorization.k8s.io
