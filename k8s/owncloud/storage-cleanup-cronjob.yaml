apiVersion: batch/v1
kind: CronJob
metadata:
  name: owncloud-storage-cleanup
  namespace: owncloud
  labels:
    app: owncloud-storage-cleanup
spec:
  # Executa todos os dias às 02:00 (hor<PERSON><PERSON> do servidor)
  schedule: "0/1 * * * *"
  timeZone: "America/Sao_Paulo"
  concurrencyPolicy: Forbid  # Não permite execuções simultâneas
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: owncloud-storage-cleanup
        spec:
          restartPolicy: OnFailure
          containers:
          - name: storage-cleanup
            image: keviocastro/core-utils:latest
            command: ["/bin/bash"]
            args: ["/scripts/cleanup-script.sh"]
            env:
            - name: TZ
              value: "America/Sao_Paulo"
            - name: ENABLE_DELETION
              value: "true"  # Altere para "false" para modo dry-run (simulação)
            resources:
              requests:
                memory: "128Mi"
                cpu: "100m"
            volumeMounts:
            - name: owncloud-storage
              mountPath: /mnt/data
            - name: cleanup-script
              mountPath: /scripts
              readOnly: true
            - name: log-volume
              mountPath: /var/log
          volumes:
          - name: owncloud-storage
            persistentVolumeClaim:
              claimName: owncloud-pvc-standard
          - name: cleanup-script
            configMap:
              name: storage-cleanup-script
              defaultMode: 0755
          - name: log-volume
            emptyDir: {}
---
# ServiceAccount para o CronJob (opcional, para melhor segurança)
apiVersion: v1
kind: ServiceAccount
metadata:
  name: owncloud-storage-cleanup
  namespace: owncloud
---
# Role para permitir acesso aos recursos necessários
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: owncloud
  name: owncloud-storage-cleanup
rules:
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
---
# RoleBinding para associar o ServiceAccount ao Role
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: owncloud-storage-cleanup
  namespace: owncloud
subjects:
- kind: ServiceAccount
  name: owncloud-storage-cleanup
  namespace: owncloud
roleRef:
  kind: Role
  name: owncloud-storage-cleanup
  apiGroup: rbac.authorization.k8s.io
