#!/bin/bash

# Script para monitorar e limpar o volume owncloud-storage
# Executa limpeza quando o espaço disponível for menor que 15%

set -e

# Configurações
MOUNT_PATH="/mnt/data"
# FILES_PATH="/home/<USER>/Downloads"
FILES_PATH="/mnt/data/files/postoaki/files"
MIN_FREE_PERCENT=${MIN_FREE_PERCENT:-"10"}
WEBHOOK_URL="https://chat.googleapis.com/v1/spaces/AAAAo3rlpb0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=YUgaBjmhx1rCIPjl8GTtEl1FemkVnVDdhexUqzzCRTs"
LOG_FILE="/var/log/owncloud-cleanup.log"

# Controle de exclusão - pode ser sobrescrito por variável de ambiente
ENABLE_DELETION=${ENABLE_DELETION:-"false"}

# Função para logging
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Função para enviar mensagem para Google Chat
send_google_chat_message() {
    local message="$1"
    local json_payload=$(cat <<EOF
{
    "text": "$message"
}
EOF
)
    
    curl -X POST \
        -H "Content-Type: application/json" \
        -d "$json_payload" \
        "$WEBHOOK_URL" \
        --silent --show-error || log "Erro ao enviar mensagem para Google Chat"
}

# Função para obter informações de espaço em disco
get_disk_usage() {
    df "$MOUNT_PATH" | awk 'NR==2 {
        total=$2*1024
        used=$3*1024
        available=$4*1024
        used_percent=$5
        gsub(/%/, "", used_percent)
        free_percent=100-used_percent
        printf "%.0f %.0f %.0f %d %d\n", total, used, available, used_percent, free_percent
    }'
}

# Função para formatar bytes em formato legível
format_bytes() {
    local bytes=$1
    if [ $bytes -ge 1099511627776 ]; then
        echo "$(echo "scale=2; $bytes/1099511627776" | bc)TB"
    elif [ $bytes -ge 1073741824 ]; then
        echo "$(echo "scale=2; $bytes/1073741824" | bc)GB"
    elif [ $bytes -ge 1048576 ]; then
        echo "$(echo "scale=2; $bytes/1048576" | bc)MB"
    else
        echo "$(echo "scale=2; $bytes/1024" | bc)KB"
    fi
}

# Função para limpar arquivos antigos
cleanup_old_files() {
    local target_free_percent=20  # Limpar até ter 20% de espaço livre
    local files_deleted=0
    local space_freed=0
    local deleted_files_list=""
    
    log "Iniciando limpeza de arquivos antigos em $FILES_PATH"
    
    if [ ! -d "$FILES_PATH" ]; then
        log "ERRO: Diretório $FILES_PATH não encontrado"
        return 1
    fi
    
    # Criar lista temporária de arquivos ordenados por data de modificação (mais antigos primeiro)
    local temp_file_list="/tmp/owncloud_files_to_delete.txt"
    find "$FILES_PATH" -type f -printf '%T@ %s %p\n' | sort -n > "$temp_file_list"
    
    while IFS=' ' read -r timestamp size filepath; do
        # Verificar espaço atual
        read total used available used_percent free_percent <<< $(get_disk_usage)
        
        if [ $free_percent -ge $target_free_percent ]; then
            log "Meta de $target_free_percent% de espaço livre atingida"
            break
        fi
        
        # Deletar arquivo (se habilitado)
        if [ -f "$filepath" ]; then
            local file_size=$(stat -c%s "$filepath" 2>/dev/null || echo "0")
            local file_date=$(date -d "@$timestamp" '+%Y-%m-%d %H:%M:%S')

            if [ "$ENABLE_DELETION" = "true" ]; then
                if rm "$filepath" 2>/dev/null; then
                    files_deleted=$((files_deleted + 1))
                    space_freed=$((space_freed + file_size))
                    deleted_files_list="${deleted_files_list}\n- $(basename "$filepath") ($(format_bytes $file_size)) - $file_date"
                    log "Arquivo deletado: $filepath ($(format_bytes $file_size))"
                else
                    log "ERRO: Não foi possível deletar $filepath"
                fi
            else
                # Modo dry-run - apenas simula a exclusão
                files_deleted=$((files_deleted + 1))
                space_freed=$((space_freed + file_size))
                deleted_files_list="${deleted_files_list}\n- $(basename "$filepath") ($(format_bytes $file_size)) - $file_date [SIMULADO]"
                log "SIMULAÇÃO: Arquivo seria deletado: $filepath ($(format_bytes $file_size))"
            fi
        fi
        
        # Limitar a 1000 arquivos por execução para evitar execução muito longa
        if [ $files_deleted -ge 1000 ]; then
            log "Limite de 1000 arquivos deletados atingido nesta execução"
            break
        fi
        
    done < "$temp_file_list"
    
    rm -f "$temp_file_list"
    
    # Obter informações finais de espaço
    read total used available used_percent free_percent <<< $(get_disk_usage)
    
    # Criar mensagem de resumo
    local mode_text=""
    if [ "$ENABLE_DELETION" = "true" ]; then
        mode_text="🗑️ *Limpeza Realizada:*"
    else
        mode_text="🔍 *Simulação de Limpeza (DRY-RUN):*"
    fi

    local summary_message="🧹 *Limpeza Automática OwnCloud - $(date '+%d/%m/%Y %H:%M')*

📊 *Status do Volume:*
• Espaço Total: $(format_bytes $total)
• Espaço Usado: $(format_bytes $used) (${used_percent}%)
• Espaço Livre: $(format_bytes $available) (${free_percent}%)

$mode_text
• Arquivos processados: $files_deleted
• Espaço que seria liberado: $(format_bytes $space_freed)

📁 *Arquivos identificados:*$deleted_files_list

🎯 *Modo:* $([ "$ENABLE_DELETION" = "true" ] && echo "EXCLUSÃO ATIVA" || echo "SIMULAÇÃO (DRY-RUN)")
🎯 *Resultado:* $([ "$ENABLE_DELETION" = "true" ] && echo "Espaço livre aumentou para ${free_percent}%" || echo "Simulação concluída - nenhum arquivo foi removido")"

    if [ "$ENABLE_DELETION" = "true" ]; then
        log "Limpeza concluída: $files_deleted arquivos deletados, $(format_bytes $space_freed) liberados"
    else
        log "Simulação concluída: $files_deleted arquivos seriam deletados, $(format_bytes $space_freed) seriam liberados"
    fi
    
    # Enviar resumo para Google Chat
    send_google_chat_message "$summary_message"
    
    return 0
}

# Função principal
main() {
    log "=== Iniciando verificação de espaço do volume owncloud-storage ==="
    
    # Verificar se o diretório de montagem existe
    if [ ! -d "$MOUNT_PATH" ]; then
        log "ERRO: Diretório de montagem $MOUNT_PATH não encontrado"
        send_google_chat_message "❌ *Erro OwnCloud Storage Monitor*: Diretório $MOUNT_PATH não encontrado"
        exit 1
    fi
    
    # Obter informações de espaço em disco
    read total used available used_percent free_percent <<< $(get_disk_usage)
    
    log "Espaço total: $(format_bytes $total)"
    log "Espaço usado: $(format_bytes $used) (${used_percent}%)"
    log "Espaço livre: $(format_bytes $available) (${free_percent}%)"
    
    # Verificar se precisa fazer limpeza
    if [ $free_percent -lt $MIN_FREE_PERCENT ]; then
        log "⚠️  ALERTA: Espaço livre (${free_percent}%) está abaixo do limite mínimo (${MIN_FREE_PERCENT}%)"

        # Enviar alerta inicial
        local mode_alert=""
        if [ "$ENABLE_DELETION" = "true" ]; then
            mode_alert="Iniciando limpeza automática..."
        else
            mode_alert="Executando simulação de limpeza (DRY-RUN)..."
        fi
        send_google_chat_message "⚠️ *Alerta OwnCloud Storage*: Espaço livre em ${free_percent}% (limite: ${MIN_FREE_PERCENT}%). $mode_alert"

        # Executar limpeza
        cleanup_old_files
    else
        log "✅ Espaço livre (${free_percent}%) está dentro do limite aceitável (>=${MIN_FREE_PERCENT}%)"
        
        # Enviar status OK (apenas uma vez por semana - segunda-feira)
        if [ $(date +%u) -eq 1 ]; then
            send_google_chat_message "✅ *Status OwnCloud Storage*: Volume saudável com ${free_percent}% de espaço livre ($(format_bytes $available))."
        fi
    fi
    
    log "=== Verificação concluída ==="
}

# Executar função principal
main "$@"
