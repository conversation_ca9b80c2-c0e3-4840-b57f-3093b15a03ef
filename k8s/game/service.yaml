apiVersion: v1
kind: Service
metadata:
  name: api-service
  namespace: game
spec:
  selector:
    app: api
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: painel-service
  namespace: game
spec:
  selector:
    app: painel
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: painel-venda-v2-service
  namespace: game
spec:
  selector:
    app: painel-venda-v2
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: painel-frota-service
  namespace: game
spec:
  selector:
    app: painel-frota
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP