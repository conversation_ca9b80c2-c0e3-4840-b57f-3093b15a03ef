apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  namespace: master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
    spec:
      nodeSelector:
        name: node-pool-hml
      containers:
        - name: api
          image: gru.ocir.io/grbvypj1mx3p/postoaki-api:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          env:
            - name: TZ
              value: "America/Sao_Paulo"
            - name: JAVA_OPTS
              value: -Dpostoaki.tempDir=/tmp -Dspring.flyway.placeholder-replacement=false -Djava.io.tmpdir=/tmp -Delastic.apm.service_name=api-hml -Dsun.jnu.encoding=UTF8 -Dfile.encoding=UTF8 -Dfile.encoding.pkg=sun.io -Dsun.io.unicode.encoding=UnicodeLittle -Duser.country=BR -Duser.language=pt -Duser.timezone=GMT-0300 -Ddebug=false -Dpostoaki.email=true -Dpostoaki.enviaremailerro=true -Dpostoaki.enviarsms=true -Dpostoaki.hostname=http://www.postoaki.app -Dpostoaki.integracao.cerrado=true -Dpostoaki.servico.awssns=true -Dspring.datasource.password="K9vL#4pQ7xM2rT8n" -Dspring.datasource.url=***************************************************** -Dspring.datasource.username=usr_api_hml -Dspring.jpa.hibernate.ddl-auto=none -Dspring.jpa.show-sql=false -Duser.timezone=Americas/Sao_Paulo -Dpostoaki.jsonlog=true -Dpostoaki.schedules=false -Dlog4j.configurationFile=log4j2-prod.xml -Dpostoaki.enviarpush=true -Dorg.quartz.threadPool.threadCount=16 -Dspring.datasource.hikari.maximum-pool-size=400 -Dspring.flyway.enabled=true -Dspringdoc.swagger-ui.enabled=true -Dspringdoc.api-docs.enabled=true -Dintegracao.yampi.url=https://api.dooki.com.br/v2/pontua -Dintegracao.yampi.token=8lBoFoKL8jQy2dbk4gnCVI2R4QlSHkxreXGfn1eg -Dintegracao.yampi.secret-key=sk_LSn3WLsp4auQIrbR6RPw0gGNwx2D39VKIDkMb -Dintegracao.yampi.webhook.pedido-criado.secret=wh_THNhOSoHI1zfGD0p2FVk2DF8MtqsRJaw0Xaw1 -Dpostoaki.cpf.tokenalas=fbcf7381c54d69c16fc5634c2456dc14 -Dintegracao.itau.url-auth=https://sts.itau.com.br/api/oauth/token -Dintegracao.itau.url-api=https://secure.api.itau/pix_recebimentos/v2 -Dintegracao.itau.grant_type=client_credentials -Ditegracao.itau.client_id=d9ca7dfe-24da-4052-b669-59980b9de517 -Dintegracao.itau.client_secret=a6b67b99-0729-400d-a399-52a9e9eb5658 -Dintegracao.itau.certificate_password=123456 -Dspring.profiles.active=prod -Dpostoaki.metabase.url=https://bi.postoaki.com.br -Dpostoaki.metabase.secret_key=97431c05d02b6d37834593d75c72c5b75d57beca7992474da93f35b665bc875e -Dpostoaki.servico-preco.url=http://apiservico.postoaki.com -Dintegracao.rvhub.url-auth=https://auth.rvhub.com.br/oauth2/token -Dintegracao.rvhub.base_url=https://api.rvhub.com.br -Dintegracao.rvhub.grant_type=client_credentials -Dintegracao.rvhub.client_id=3vapa4b1pan2m4ddo7ht9au9hh -Dintegracao.rvhub.client_secret=1u50dm7sddk4i79absctcmkohvtndfefoumlst5fp2v0vf4h1jre -javaagent:elastic-apm-agent.jar -Delastic.apm.application_packages=com.coffeeincode.postoaki -Delastic.apm.server_url=https://84c7e643a2b54faab87df54b5d6beee4.apm.us-east-2.aws.elastic-cloud.com:443 -Delastic.apm.capture_body=ALL -Delastic.apm.api_key=OEJqUllKUUJQbTRuMEtZek1LSmw6Tm1tT21hUTlRTTJMcUp1Y0NYaGhnQQ== -Delastic.apm.environment=prod
          # resources:
          #   requests:
          #     cpu: "100m"
          #     memory: "128Mi"
          #   limits:
          #     cpu: "500m"
          #     memory: "512Mi"
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 10
      imagePullSecrets:
        - name: docker-registry
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: painel
  namespace: master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: painel
  template:
    metadata:
      labels:
        app: painel
    spec:
      nodeSelector:
        name: node-pool-hml
      containers:
        - name: painel
          image: gru.ocir.io/grbvypj1mx3p/postoaki-painel:20250523131500
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          # resources:
          #   requests:
          #     cpu: "100m"
          #     memory: "128Mi"
          #   limits:
          #     cpu: "500m"
          #     memory: "512Mi"
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          lifecycle:
            postStart:
              exec:
                command: ["/bin/sh", "/scripts/replace-urls.sh"]
          volumeMounts:
            - name: url-replace-script
              mountPath: /scripts
      volumes:
        - name: url-replace-script
          configMap:
            name: url-replace-script
            defaultMode: 0755
      imagePullSecrets:
        - name: docker-registry
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: painel-frota
  namespace: master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: painel-frota
  template:
    metadata:
      labels:
        app: painel-frota
    spec:
      nodeSelector:
        name: node-pool-hml
      containers:
        - name: painel-frota
          image: gru.ocir.io/grbvypj1mx3p/postoaki-painel-frota:20250424042047
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          # resources:
          #   requests:
          #     cpu: "100m"
          #     memory: "128Mi"
          #   limits:
          #     cpu: "500m"
          #     memory: "512Mi"
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          lifecycle:
            postStart:
              exec:
                command: ["/bin/sh", "/scripts/replace-urls.sh"]
          volumeMounts:
            - name: url-replace-script
              mountPath: /scripts
      volumes:
        - name: url-replace-script
          configMap:
            name: url-replace-script
            defaultMode: 0755
      imagePullSecrets:
        - name: docker-registry
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: painel-venda-v2
  namespace: master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: painel-venda-v2
  template:
    metadata:
      labels:
        app: painel-venda-v2
    spec:
      nodeSelector:
        name: node-pool-hml
      containers:
        - name: painel-venda-v2
          image: gru.ocir.io/grbvypj1mx3p/postoaki-vendas-v2:20250513021104
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          # resources:
          #   requests:
          #     cpu: "100m"
          #     memory: "128Mi"
          #   limits:
          #     cpu: "500m"
          #     memory: "512Mi"
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          lifecycle:
            postStart:
              exec:
                command: ["/bin/sh", "/scripts/replace-urls.sh"]
          volumeMounts:
            - name: url-replace-script
              mountPath: /scripts
      volumes:
        - name: url-replace-script
          configMap:
            name: url-replace-script
            defaultMode: 0755
      imagePullSecrets:
        - name: docker-registry
