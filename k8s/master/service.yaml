apiVersion: v1
kind: Service
metadata:
  name: api
  namespace: master
spec:
  selector:
    app: api
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: painel
  namespace: master
spec:
  selector:
    app: painel
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: painel-venda-v2
  namespace: master
spec:
  selector:
    app: painel-venda-v2
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: painel-frota
  namespace: master
spec:
  selector:
    app: painel-frota
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP