apiVersion: apps/v1
kind: Deployment
metadata:
  name: painel-deployment
  namespace: painel
spec:
  replicas: 1
  selector:
    matchLabels:
      app: painel
  template:
    metadata:
      labels:
        app: painel
    spec:
      containers:
        - name: painel
          image: gru.ocir.io/grbvypj1mx3p/postoaki-painel:20250825122657
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
      imagePullSecrets:
        - name: docker-registry