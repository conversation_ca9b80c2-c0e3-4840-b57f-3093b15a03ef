apiVersion: apps/v1
kind: Deployment
metadata:
  name: painel-venda-v2-deployment
  namespace: painel
spec:
  replicas: 1
  selector:
    matchLabels:
      app: painel-venda-v2
  template:
    metadata:
      labels:
        app: painel-venda-v2
    spec:
      containers:
        - name: painel-venda-v2
          image: gru.ocir.io/grbvypj1mx3p/postoaki-vendas-v2:20250813024032
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
      imagePullSecrets:
        - name: docker-registry

