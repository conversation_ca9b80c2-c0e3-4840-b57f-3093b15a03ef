apiVersion: batch/v1
kind: CronJob
metadata:
  name: increase-replicas-cronjob
  namespace: api
spec:
  schedule: "10 3,10,15,17 * * 1-5"
  timeZone: "America/Sao_Paulo"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: scale-hpa
              image: bitnami/kubectl:latest
              imagePullPolicy: Always
              command:
                - /bin/sh
                - -c
                - |
                  kubectl patch hpa api-deployment-hpa -n api --patch '{"spec": {"minReplicas": 4}}'
          restartPolicy: OnFailure
          serviceAccountName: scale-hpa-sa
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: decrease-replicas-cronjob
  namespace: api
spec:
  schedule: "0 4,14,16,20 * * 1-5"
  timeZone: "America/Sao_Paulo"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: scale-hpa
              image: bitnami/kubectl:latest
              imagePullPolicy: Always
              command:
                - /bin/sh
                - -c
                - |
                  kubectl patch hpa api-deployment-hpa -n api --patch '{"spec": {"minReplicas": 2}}'
          restartPolicy: OnFailure
          serviceAccountName: scale-hpa-sa
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: scale-hpa-sa
  namespace: api
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: api
  name: scale-hpa-role
rules:
  - apiGroups: ["autoscaling"]
    resources: ["horizontalpodautoscalers"]
    verbs: ["patch", "get", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: scale-hpa-rolebinding
  namespace: api
subjects:
  - kind: ServiceAccount
    name: scale-hpa-sa
    namespace: api
roleRef:
  kind: Role
  name: scale-hpa-role
  apiGroup: rbac.authorization.k8s.io
